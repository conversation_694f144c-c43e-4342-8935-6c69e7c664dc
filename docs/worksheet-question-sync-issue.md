# Worksheet Question Synchronization Issue

## Problem Description

When removing questions from worksheets, questions were being successfully removed from PostgreSQL but remained in MongoDB, causing data inconsistency between the two databases.

## Root Cause Analysis

The issue was in the `removeQuestionFromPromptResult` method in `WorksheetQuestionService`. The method was **silently failing** when questions couldn't be found in MongoDB, returning `null` without throwing an error or logging the failure.

### Original Problematic Code

```typescript
private async removeQuestionFromPromptResult(
  worksheetId: string,
  questionId: string
): Promise<ExerciseQuestionItem | null> {
  const result = await this.findQuestionInPromptResult(worksheetId, questionId);
  if (!result) {
    return null; // ⚠️ SILENT FAILURE - No error thrown or logged
  }
  // ... removal logic
}
```

### Potential Causes of Silent Failures

1. **WorksheetPromptResult document not found** - Document doesn't exist in MongoDB
2. **Questions array missing** - Document exists but `promptResult.result` is null/undefined
3. **Question ID mismatch** - Question ID format doesn't match between PostgreSQL and MongoDB
4. **Missing question IDs** - Questions in MongoDB missing the `id` field
5. **Database connection issues** - MongoDB operations failing silently

## Solution Implemented

### 1. Enhanced Error Handling and Logging

Updated `removeQuestionFromPromptResult` method to:
- Throw explicit `NotFoundException` when document or question not found
- Add detailed logging for debugging
- Log all available question IDs when search fails
- Provide clear error messages indicating the specific failure reason

### 2. Improved Debugging in `findQuestionInPromptResult`

Added comprehensive logging to help identify:
- Available question IDs in MongoDB
- Question ID being searched for
- Index where question was found (or not found)

### 3. Diagnostic Script

Created `scripts/debug-worksheet-question-sync.ts` with commands:

```bash
# Analyze all worksheets for sync issues
npm run debug-sync analyze

# Debug a specific worksheet
npm run debug-sync debug <worksheet-id>

# Fix sync issues (dry run)
npm run debug-sync fix <worksheet-id>

# Apply fixes
npm run debug-sync fix <worksheet-id> --apply
```

### 4. Unit Tests

Added comprehensive tests in `worksheet-question-sync.spec.ts` to verify:
- Proper error handling when documents not found
- Correct behavior when questions missing
- Successful removal when questions exist
- Handling of malformed data

## Data Structure Overview

### PostgreSQL (Worksheet Entity)
```typescript
{
  id: string;
  questionIds: string[]; // Array of question UUIDs
  // ... other fields
}
```

### MongoDB (WorksheetPromptResult Collection)
```typescript
{
  worksheetId: string;
  promptResult: {
    result: [
      {
        id: string; // Must match PostgreSQL questionIds
        type: string;
        content: string;
        // ... other question fields
      }
    ]
  };
  // ... other fields
}
```

## Prevention Measures

### 1. Explicit Error Handling
- All MongoDB operations now throw exceptions on failure
- No more silent `null` returns that mask issues

### 2. Enhanced Logging
- Debug-level logging for all question operations
- Question ID comparisons logged for troubleshooting

### 3. Validation Checks
- Verify document existence before operations
- Check for required fields (question IDs)
- Log available data when searches fail

### 4. Monitoring Tools
- Diagnostic script for ongoing monitoring
- Unit tests to catch regressions
- Clear error messages for easier debugging

## Usage Instructions

### For Developers

1. **When debugging sync issues:**
   ```bash
   npm run debug-sync debug <worksheet-id>
   ```

2. **To find all worksheets with sync problems:**
   ```bash
   npm run debug-sync analyze
   ```

3. **To fix a specific worksheet:**
   ```bash
   # Dry run first
   npm run debug-sync fix <worksheet-id>
   
   # Apply if dry run looks good
   npm run debug-sync fix <worksheet-id> --apply
   ```

### For Operations

1. **Monitor logs** for new error messages that indicate sync issues
2. **Run periodic analysis** to catch sync problems early
3. **Use diagnostic script** to investigate user reports of missing questions

## Expected Behavior After Fix

1. **Question removal failures** will now throw clear exceptions instead of silently failing
2. **Detailed logs** will help identify the exact cause of any sync issues
3. **Diagnostic tools** will help quickly identify and fix any remaining problems
4. **Unit tests** will prevent regression of this issue

## Testing the Fix

Run the unit tests:
```bash
npm test -- worksheet-question-sync.spec.ts
```

Test with a real worksheet:
```bash
npm run debug-sync debug <actual-worksheet-id>
```

The enhanced error handling should now clearly indicate if there are any remaining sync issues and provide actionable information for resolving them.
