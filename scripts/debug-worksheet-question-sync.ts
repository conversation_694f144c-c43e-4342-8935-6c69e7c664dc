#!/usr/bin/env ts-node

/**
 * Debug script to identify and fix worksheet question synchronization issues
 * between PostgreSQL and MongoDB
 */

import { DataSource } from 'typeorm';
import * as mongoose from 'mongoose';
import { config } from 'dotenv';

// Load environment variables
config();

// MongoDB Schema
const WorksheetPromptResultSchema = new mongoose.Schema({
  worksheetId: { type: String, required: true },
  promptResult: { type: Object, required: true },
  currentQuestionCount: { type: Number, default: 0 },
  totalQuestionCount: { type: Number, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, { timestamps: true });

// PostgreSQL Entity (simplified)
interface Worksheet {
  id: string;
  questionIds?: string[];
  title?: string;
  schoolId?: string;
}

class WorksheetQuestionSyncDebugger {
  private mongoConnection: mongoose.Connection;
  private pgDataSource: DataSource;
  private WorksheetPromptResultModel: mongoose.Model<any>;

  constructor() {
    // Initialize MongoDB connection
    this.mongoConnection = mongoose.createConnection(process.env.MONGODB_URI!);
    this.WorksheetPromptResultModel = this.mongoConnection.model('WorksheetPromptResult', WorksheetPromptResultSchema);

    // Initialize PostgreSQL connection
    this.pgDataSource = new DataSource({
      type: 'postgres',
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '5432'),
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      entities: [],
      synchronize: false,
      logging: false,
    });
  }

  async connect(): Promise<void> {
    console.log('🔌 Connecting to databases...');
    
    // Connect to PostgreSQL
    await this.pgDataSource.initialize();
    console.log('✅ Connected to PostgreSQL');

    // MongoDB connection is already established in constructor
    console.log('✅ Connected to MongoDB');
  }

  async disconnect(): Promise<void> {
    await this.pgDataSource.destroy();
    await this.mongoConnection.close();
    console.log('🔌 Disconnected from databases');
  }

  /**
   * Find worksheets with synchronization issues
   */
  async findSyncIssues(): Promise<void> {
    console.log('\n🔍 Analyzing worksheet question synchronization...\n');

    // Get all worksheets from PostgreSQL
    const worksheets = await this.pgDataSource.query(`
      SELECT id, "questionIds", title, "schoolId"
      FROM worksheets
      WHERE "questionIds" IS NOT NULL
      AND jsonb_array_length("questionIds") > 0
      ORDER BY "createdAt" DESC
      LIMIT 50
    `);

    console.log(`📊 Found ${worksheets.length} worksheets with questions in PostgreSQL`);

    let syncIssues = 0;
    let totalChecked = 0;

    for (const worksheet of worksheets) {
      totalChecked++;
      const issues = await this.checkWorksheetSync(worksheet);
      if (issues.length > 0) {
        syncIssues++;
        console.log(`\n❌ SYNC ISSUES FOUND for worksheet ${worksheet.id}:`);
        console.log(`   Title: ${worksheet.title}`);
        console.log(`   School ID: ${worksheet.schoolId}`);
        issues.forEach(issue => console.log(`   - ${issue}`));
      }
    }

    console.log(`\n📈 Summary:`);
    console.log(`   Total worksheets checked: ${totalChecked}`);
    console.log(`   Worksheets with sync issues: ${syncIssues}`);
    console.log(`   Sync success rate: ${((totalChecked - syncIssues) / totalChecked * 100).toFixed(1)}%`);
  }

  /**
   * Check synchronization for a specific worksheet
   */
  private async checkWorksheetSync(worksheet: Worksheet): Promise<string[]> {
    const issues: string[] = [];
    const pgQuestionIds = worksheet.questionIds || [];

    // Get MongoDB data
    const mongoDoc = await this.WorksheetPromptResultModel.findOne({
      worksheetId: worksheet.id
    });

    if (!mongoDoc) {
      issues.push('WorksheetPromptResult document not found in MongoDB');
      return issues;
    }

    if (!mongoDoc.promptResult?.result) {
      issues.push('No questions array found in MongoDB document');
      return issues;
    }

    const mongoQuestions = mongoDoc.promptResult.result;
    const mongoQuestionIds = mongoQuestions.map((q: any) => q.id).filter(Boolean);

    // Compare counts
    if (pgQuestionIds.length !== mongoQuestionIds.length) {
      issues.push(`Question count mismatch: PostgreSQL has ${pgQuestionIds.length}, MongoDB has ${mongoQuestionIds.length}`);
    }

    // Check for missing questions in MongoDB
    const missingInMongo = pgQuestionIds.filter(id => !mongoQuestionIds.includes(id));
    if (missingInMongo.length > 0) {
      issues.push(`Questions missing in MongoDB: [${missingInMongo.join(', ')}]`);
    }

    // Check for extra questions in MongoDB
    const extraInMongo = mongoQuestionIds.filter(id => !pgQuestionIds.includes(id));
    if (extraInMongo.length > 0) {
      issues.push(`Extra questions in MongoDB: [${extraInMongo.join(', ')}]`);
    }

    // Check for questions without IDs in MongoDB
    const questionsWithoutIds = mongoQuestions.filter((q: any) => !q.id);
    if (questionsWithoutIds.length > 0) {
      issues.push(`${questionsWithoutIds.length} questions in MongoDB missing ID field`);
    }

    return issues;
  }

  /**
   * Debug a specific worksheet
   */
  async debugWorksheet(worksheetId: string): Promise<void> {
    console.log(`\n🔍 Debugging worksheet: ${worksheetId}\n`);

    // Get PostgreSQL data
    const pgWorksheet = await this.pgDataSource.query(`
      SELECT id, "questionIds", title, "schoolId", "createdAt", "updatedAt"
      FROM worksheets 
      WHERE id = $1
    `, [worksheetId]);

    if (pgWorksheet.length === 0) {
      console.log('❌ Worksheet not found in PostgreSQL');
      return;
    }

    const worksheet = pgWorksheet[0];
    console.log('📋 PostgreSQL Data:');
    console.log(`   ID: ${worksheet.id}`);
    console.log(`   Title: ${worksheet.title}`);
    console.log(`   School ID: ${worksheet.schoolId}`);
    console.log(`   Question IDs: [${(worksheet.questionIds || []).join(', ')}]`);
    console.log(`   Question Count: ${(worksheet.questionIds || []).length}`);

    // Get MongoDB data
    const mongoDoc = await this.WorksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    console.log('\n🍃 MongoDB Data:');
    if (!mongoDoc) {
      console.log('   ❌ WorksheetPromptResult document not found');
      return;
    }

    console.log(`   Document ID: ${mongoDoc._id}`);
    console.log(`   Worksheet ID: ${mongoDoc.worksheetId}`);
    console.log(`   Current Question Count: ${mongoDoc.currentQuestionCount}`);
    console.log(`   Total Question Count: ${mongoDoc.totalQuestionCount}`);

    if (!mongoDoc.promptResult?.result) {
      console.log('   ❌ No questions array found');
      return;
    }

    const mongoQuestions = mongoDoc.promptResult.result;
    const mongoQuestionIds = mongoQuestions.map((q: any, index: number) => ({
      index,
      id: q.id,
      type: q.type,
      hasContent: !!q.content
    }));

    console.log(`   Questions in MongoDB: ${mongoQuestions.length}`);
    console.log('   Question Details:');
    mongoQuestionIds.forEach(q => {
      console.log(`     [${q.index}] ID: ${q.id || 'MISSING'}, Type: ${q.type || 'UNKNOWN'}, Has Content: ${q.hasContent}`);
    });

    // Perform sync analysis
    const issues = await this.checkWorksheetSync(worksheet);
    if (issues.length > 0) {
      console.log('\n❌ Synchronization Issues:');
      issues.forEach(issue => console.log(`   - ${issue}`));
    } else {
      console.log('\n✅ No synchronization issues found');
    }
  }

  /**
   * Fix synchronization issues for a specific worksheet
   */
  async fixWorksheetSync(worksheetId: string, dryRun: boolean = true): Promise<void> {
    console.log(`\n🔧 ${dryRun ? 'DRY RUN - ' : ''}Fixing worksheet sync: ${worksheetId}\n`);

    // Get current state
    const pgWorksheet = await this.pgDataSource.query(`
      SELECT id, "questionIds", title
      FROM worksheets 
      WHERE id = $1
    `, [worksheetId]);

    if (pgWorksheet.length === 0) {
      console.log('❌ Worksheet not found in PostgreSQL');
      return;
    }

    const worksheet = pgWorksheet[0];
    const pgQuestionIds = worksheet.questionIds || [];

    const mongoDoc = await this.WorksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!mongoDoc || !mongoDoc.promptResult?.result) {
      console.log('❌ MongoDB document not found or invalid');
      return;
    }

    const mongoQuestions = mongoDoc.promptResult.result;
    const mongoQuestionIds = mongoQuestions.map((q: any) => q.id).filter(Boolean);

    console.log(`PostgreSQL has ${pgQuestionIds.length} question IDs`);
    console.log(`MongoDB has ${mongoQuestionIds.length} questions with IDs`);

    // Remove questions from MongoDB that are not in PostgreSQL
    const toRemoveFromMongo = mongoQuestions.filter((q: any) => 
      q.id && !pgQuestionIds.includes(q.id)
    );

    if (toRemoveFromMongo.length > 0) {
      console.log(`\n🗑️  Removing ${toRemoveFromMongo.length} extra questions from MongoDB:`);
      toRemoveFromMongo.forEach((q: any) => {
        console.log(`   - ${q.id} (${q.type})`);
      });

      if (!dryRun) {
        const filteredQuestions = mongoQuestions.filter((q: any) => 
          !q.id || pgQuestionIds.includes(q.id)
        );

        await this.WorksheetPromptResultModel.updateOne(
          { worksheetId: worksheetId },
          {
            $set: {
              'promptResult.result': filteredQuestions,
              currentQuestionCount: filteredQuestions.length,
              totalQuestionCount: filteredQuestions.length,
              updatedAt: new Date()
            }
          }
        );
        console.log('✅ Removed extra questions from MongoDB');
      }
    }

    console.log(dryRun ? '\n🔍 DRY RUN COMPLETE - No changes made' : '\n✅ SYNC FIX COMPLETE');
  }
}

// Main execution
async function main() {
  const syncDebugger = new WorksheetQuestionSyncDebugger();

  try {
    await syncDebugger.connect();

    const args = process.argv.slice(2);
    const command = args[0];
    const worksheetId = args[1];

    switch (command) {
      case 'analyze':
        await syncDebugger.findSyncIssues();
        break;

      case 'debug':
        if (!worksheetId) {
          console.log('Usage: npm run debug-sync debug <worksheet-id>');
          process.exit(1);
        }
        await syncDebugger.debugWorksheet(worksheetId);
        break;

      case 'fix':
        if (!worksheetId) {
          console.log('Usage: npm run debug-sync fix <worksheet-id> [--apply]');
          process.exit(1);
        }
        const apply = args.includes('--apply');
        await syncDebugger.fixWorksheetSync(worksheetId, !apply);
        break;

      default:
        console.log('Usage:');
        console.log('  npm run debug-sync analyze           - Find all sync issues');
        console.log('  npm run debug-sync debug <id>        - Debug specific worksheet');
        console.log('  npm run debug-sync fix <id>          - Dry run fix for worksheet');
        console.log('  npm run debug-sync fix <id> --apply  - Apply fix for worksheet');
        break;
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    await syncDebugger.disconnect();
  }
}

if (require.main === module) {
  main();
}
